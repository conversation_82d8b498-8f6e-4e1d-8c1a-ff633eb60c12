# 认证核身等5个需求功能测试用例

## 需求1：认证核身接口增加传参控制是否展示协议

### 个人核身网页接口测试
#### TL-001 showAgreement参数为true时展示协议
##### 前置条件
- 测试环境已部署最新版本
- 个人核身网页接口可正常访问
- 准备有效的个人身份信息

##### 操作步骤
1. 调用个人核身网页接口
2. 传入showAgreement=true参数
3. 观察返回结果和页面展示

##### 预期结果
- 接口调用成功
- 页面展示数字证书协议、服务协议和隐私政策
- 短信中包含协议链接
- 根据认证用途展示企业证书协议

#### TL-002 showAgreement参数为false时不展示协议
##### 前置条件
- 测试环境已部署最新版本
- 个人核身网页接口可正常访问

##### 操作步骤
1. 调用个人核身网页接口
2. 传入showAgreement=false参数
3. 观察返回结果和页面展示

##### 预期结果
- 接口调用成功
- 页面不展示协议相关内容
- 短信中不包含协议链接

#### TL-003 showAgreement参数缺省时默认不展示协议
##### 前置条件
- 测试环境已部署最新版本
- 个人核身网页接口可正常访问

##### 操作步骤
1. 调用个人核身网页接口
2. 不传入showAgreement参数
3. 观察返回结果和页面展示

##### 预期结果
- 接口调用成功
- 页面不展示协议相关内容（默认false）
- 短信中不包含协议链接

### 个人核身扫脸API测试
#### TL-004 扫脸API showAgreement=true协议展示
##### 前置条件
- 测试环境已部署最新版本
- 个人核身扫脸API可正常访问
- 准备有效的个人身份信息

##### 操作步骤
1. 调用个人核身扫脸API
2. 传入showAgreement=true参数
3. 完成扫脸认证流程
4. 观察协议展示情况

##### 预期结果
- API调用成功
- 扫脸页面展示相关协议
- 认证流程正常完成

### 个人核身三要素API测试
#### TL-005 三要素API协议控制功能验证
##### 前置条件
- 测试环境已部署最新版本
- 个人核身三要素API可正常访问

##### 操作步骤
1. 调用个人核身三要素API
2. 分别测试showAgreement=true和false
3. 验证协议展示差异

##### 预期结果
- true时展示协议，false时不展示
- API功能正常，不影响核身逻辑

### 个人核身四要素API测试
#### TL-006 四要素API协议控制功能验证
##### 前置条件
- 测试环境已部署最新版本
- 个人核身四要素API可正常访问

##### 操作步骤
1. 调用个人核身四要素API
2. 测试showAgreement参数各种取值
3. 验证协议展示逻辑

##### 预期结果
- 参数控制协议展示正常
- 不影响四要素核身功能

## 需求2：V3账号解绑链接支持微信小程序redirectUrl

### 微信小程序解绑功能测试
#### TL-007 微信小程序解绑传入wechat://back
##### 前置条件
- 微信小程序环境可用
- 用户已绑定V3账号
- 解绑接口已支持新参数

##### 操作步骤
1. 在微信小程序中发起解绑请求
2. redirectUrl参数传入"wechat://back"
3. 完成解绑流程
4. 观察解绑成功后的跳转行为

##### 预期结果
- 解绑接口接受wechat://back参数
- 解绑成功后唤起微信back方法
- 用户返回到小程序上一页面

#### TL-008 非微信小程序环境解绑功能验证
##### 前置条件
- 非微信小程序环境（如H5、PC）
- 用户已绑定V3账号

##### 操作步骤
1. 在非微信环境发起解绑
2. 测试redirectUrl各种取值
3. 验证解绑功能正常性

##### 预期结果
- 解绑功能正常
- redirectUrl按原逻辑处理
- 不影响现有解绑流程

## 需求3：国际短信扣费子产品上游适配

### 国际短信计费测试
#### TL-009 国际手机号短信发送计费验证
##### 前置条件
- 测试环境已配置国际短信计费
- 准备国际手机号码
- 扣费子产品service-C-127已配置

##### 操作步骤
1. 使用国际手机号调用意愿认证短信接口
2. 发送验证码短信
3. 检查计费记录和扣费金额

##### 预期结果
- 短信发送成功
- 按国际短信标准计费
- 扣费子产品code为service-C-127

#### TL-010 国内手机号短信发送计费验证
##### 前置条件
- 测试环境已配置短信计费
- 准备国内手机号码

##### 操作步骤
1. 使用国内手机号调用意愿认证短信接口
2. 发送验证码短信
3. 检查计费记录和扣费金额

##### 预期结果
- 短信发送成功
- 按国内短信标准计费
- 计费逻辑与国际短信区分

### 白名单控制测试
#### TL-011 存量客户白名单国际短信计费
##### 前置条件
- 存量客户已配置白名单
- 客户使用国际手机号

##### 操作步骤
1. 白名单客户使用国际手机号发送短信
2. 检查计费方式
3. 验证是否按普通短信计费

##### 预期结果
- 白名单客户按普通短信计费
- 非白名单客户按国际短信计费

## 需求4：企业法人意愿认证方式调整

### 认证方式变更测试
#### TL-012 V2企业实名认证方式验证
##### 前置条件
- V2版本企业实名页面可访问
- 准备企业法人身份信息
- 企业四要素信息准备完整

##### 操作步骤
1. 访问V2版本企业实名页面
2. 进行法人刷脸认证
3. 完成组织机构四要素信息比对
4. 查看认证详情显示

##### 预期结果
- 认证方式显示为"法人刷脸+组织机构四要素信息比对"
- 不再显示"组织机构三要素信息比对"
- 认证流程正常完成

### 出证显示测试
#### TL-013 存出证认证方式显示验证
##### 前置条件
- 企业已完成新认证方式实名
- 存出证系统可正常访问

##### 操作步骤
1. 查询企业存出证信息
2. 检查认证方式显示内容
3. 验证认证详情准确性

##### 预期结果
- 存出证显示新的认证方式
- 认证信息准确无误

#### TL-014 运营支撑平台实名详情查看
##### 前置条件
- 运营支撑平台可正常访问
- 企业实名数据存在

##### 操作步骤
1. 登录运营支撑平台
2. 查看企业实名详情
3. 检查认证方式显示

##### 预期结果
- 平台显示新的认证方式
- 详情信息完整准确

## 需求5：SSO登录支持人机校验

### 人机校验集成测试
#### TL-015 SSO登录人机校验完整流程
##### 前置条件
- SSO登录功能正常
- 极验服务可用
- 准备测试手机号

##### 操作步骤
1. 调用人机校验接口获取challenge
2. 完成前端人机校验
3. 调用发送验证码接口传入极验信息
4. 完成绑定三方账号+登录

##### 预期结果
- 人机校验接口返回正确参数
- 验证码发送成功
- 登录流程正常完成

### 极验降级测试
#### TL-016 极验降级机制验证
##### 前置条件
- 极验服务配置降级策略
- SSO登录功能正常

##### 操作步骤
1. 模拟极验服务异常
2. 触发降级机制
3. 验证登录流程是否正常

##### 预期结果
- 降级后不展示极验
- 后端不进行极验校验
- 登录功能正常使用

### 多端适配测试
#### TL-017 PC端人机校验功能验证
##### 前置条件
- PC端SSO登录页面可访问
- 极验组件正常加载

##### 操作步骤
1. PC端访问SSO登录页面
2. 完成人机校验流程
3. 验证登录功能

##### 预期结果
- PC端人机校验正常显示
- 校验流程顺畅
- 登录成功

#### TL-018 H5端人机校验功能验证
##### 前置条件
- H5端SSO登录页面可访问
- 移动端极验组件正常

##### 操作步骤
1. H5端访问SSO登录页面
2. 完成移动端人机校验
3. 验证登录功能

##### 预期结果
- H5端人机校验适配良好
- 移动端操作体验正常
- 登录功能正常

## 第五步：测试用例优化

### 异常场景补充测试用例

#### MYTL-001 showAgreement参数异常值处理
##### 前置条件
- 个人核身接口可正常访问

##### 操作步骤
1. 传入showAgreement为非boolean值（如字符串、数字）
2. 传入null、undefined等特殊值
3. 观察接口处理结果

##### 预期结果
- 接口能正确处理异常参数
- 返回适当的错误信息或默认处理

#### MYTL-002 微信小程序解绑异常redirectUrl处理
##### 前置条件
- 微信小程序环境
- 用户已绑定账号

##### 操作步骤
1. 传入格式错误的redirectUrl
2. 传入超长redirectUrl
3. 传入特殊字符redirectUrl

##### 预期结果
- 接口能正确验证redirectUrl格式
- 异常情况下有合理的错误提示

#### MYTL-003 国际短信号码格式边界测试
##### 前置条件
- 短信发送接口可用

##### 操作步骤
1. 测试各国家地区号码格式
2. 测试边界长度的手机号
3. 测试格式错误的国际号码

##### 预期结果
- 正确识别国际号码格式
- 错误格式给出明确提示
- 计费逻辑准确区分

#### MYTL-004 企业认证信息不完整场景
##### 前置条件
- V2企业实名页面可访问

##### 操作步骤
1. 提供不完整的企业四要素信息
2. 法人刷脸失败场景测试
3. 组织机构信息错误场景

##### 预期结果
- 系统能正确提示缺失信息
- 认证失败有明确的错误说明
- 不影响正常认证流程

#### MYTL-005 极验服务异常场景处理
##### 前置条件
- SSO登录功能正常

##### 操作步骤
1. 模拟极验接口超时
2. 模拟极验返回错误数据
3. 模拟网络异常情况

##### 预期结果
- 系统能正确处理极验异常
- 降级机制正常工作
- 用户体验不受严重影响

### 性能和并发测试用例

#### PATL-001 核身接口高并发协议展示测试
##### 前置条件
- 测试环境性能稳定
- 准备并发测试工具

##### 操作步骤
1. 模拟1000个并发请求调用核身接口
2. 50%请求showAgreement=true，50%为false
3. 监控接口响应时间和成功率

##### 预期结果
- 接口响应时间在可接受范围内
- 成功率达到99%以上
- 协议展示逻辑不影响性能

#### PATL-002 国际短信发送性能测试
##### 前置条件
- 短信服务正常
- 准备大量国际号码

##### 操作步骤
1. 批量发送国际短信
2. 监控发送成功率和响应时间
3. 检查计费准确性

##### 预期结果
- 国际短信发送成功率正常
- 计费逻辑不影响发送性能
- 系统稳定性良好

#### PATL-003 SSO登录人机校验性能测试
##### 前置条件
- SSO登录系统稳定
- 极验服务正常

##### 操作步骤
1. 模拟高并发登录请求
2. 每个请求都包含人机校验
3. 监控整体登录性能

##### 预期结果
- 人机校验不显著影响登录性能
- 系统能承受正常业务压力
- 降级机制在高负载下正常工作

## 第六步：测试用例评审及补充遗漏场景

### 兼容性测试补充

#### TL-019 新老版本接口兼容性验证
##### 前置条件
- 新老版本系统并存
- 接口版本管理正常

##### 操作步骤
1. 老版本客户端调用新接口
2. 新版本客户端调用老接口
3. 验证向前向后兼容性

##### 预期结果
- 新增参数不影响老版本使用
- 接口兼容性良好
- 功能正常降级

#### TL-020 多浏览器环境测试
##### 前置条件
- 准备Chrome、Firefox、Safari、Edge浏览器
- 各浏览器版本为主流版本

##### 操作步骤
1. 在各浏览器中测试核身协议展示
2. 测试SSO登录人机校验
3. 验证微信小程序内嵌H5功能

##### 预期结果
- 各浏览器功能表现一致
- 协议展示样式正常
- 人机校验组件正常加载

### 安全性测试补充

#### TL-021 参数注入安全测试
##### 前置条件
- 测试环境安全配置正常

##### 操作步骤
1. 尝试在showAgreement参数中注入恶意代码
2. 测试redirectUrl参数的XSS攻击
3. 验证接口输入过滤机制

##### 预期结果
- 系统能正确过滤恶意输入
- 不存在注入漏洞
- 安全机制正常工作

#### TL-022 人机校验绕过测试
##### 前置条件
- SSO登录系统正常
- 了解极验校验机制

##### 操作步骤
1. 尝试绕过人机校验直接登录
2. 测试重放攻击可能性
3. 验证校验结果验证机制

##### 预期结果
- 无法绕过人机校验
- 系统能检测异常请求
- 安全防护机制有效

## 第七步：冒烟测试用例提取

### 核心功能冒烟测试

#### SMOKE-001 核身接口协议展示基本功能
##### 前置条件
- 系统部署完成

##### 操作步骤
1. 调用个人核身网页接口，showAgreement=true
2. 验证协议正常展示

##### 预期结果
- 接口调用成功，协议正常展示

#### SMOKE-002 微信小程序解绑基本功能
##### 前置条件
- 微信小程序环境可用

##### 操作步骤
1. 微信小程序中解绑账号，redirectUrl="wechat://back"
2. 验证解绑成功并正确跳转

##### 预期结果
- 解绑成功，唤起微信back方法

#### SMOKE-003 国际短信计费基本功能
##### 前置条件
- 短信服务正常

##### 操作步骤
1. 使用国际号码发送验证码短信
2. 检查计费记录

##### 预期结果
- 短信发送成功，按国际标准计费

#### SMOKE-004 企业认证方式基本功能
##### 前置条件
- V2企业实名页面可用

##### 操作步骤
1. 完成法人刷脸+四要素认证
2. 查看认证详情

##### 预期结果
- 认证成功，显示新的认证方式

#### SMOKE-005 SSO人机校验基本功能
##### 前置条件
- SSO登录系统正常

##### 操作步骤
1. 完成人机校验后登录
2. 验证登录成功

##### 预期结果
- 人机校验通过，登录成功

## 第八步：线上验证用例提取

### 生产环境验证用例

#### PROD-001 核身接口生产环境验证
##### 前置条件
- 生产环境已发布新版本
- 准备真实用户数据

##### 操作步骤
1. 生产环境调用核身接口
2. 验证showAgreement参数生效
3. 监控接口调用量和成功率

##### 预期结果
- 生产功能正常
- 无异常报错
- 性能指标正常

#### PROD-002 国际短信生产计费验证
##### 前置条件
- 生产环境短信服务正常
- 计费系统已更新

##### 操作步骤
1. 监控国际短信发送量
2. 检查计费准确性
3. 验证客户扣费正常

##### 预期结果
- 国际短信计费准确
- 客户账单正确
- 无计费异常

#### PROD-003 SSO登录生产环境验证
##### 前置条件
- 生产环境SSO系统正常
- 极验服务稳定

##### 操作步骤
1. 监控SSO登录成功率
2. 检查人机校验通过率
3. 观察用户反馈

##### 预期结果
- 登录成功率正常
- 用户体验良好
- 无明显投诉

## 第九步：生成符合XMind导入标准的markdown文件

### 测试用例总结

**总计测试用例数量：**
- 基础功能测试用例：18个（TL-001至TL-018）
- 优化测试用例：5个（MYTL-001至MYTL-005）
- 性能测试用例：3个（PATL-001至PATL-003）
- 补充测试用例：4个（TL-019至TL-022）
- 冒烟测试用例：5个（SMOKE-001至SMOKE-005）
- 生产验证用例：3个（PROD-001至PROD-003）

**覆盖范围：**
- 功能测试：100%覆盖5个需求的核心功能
- 异常测试：覆盖主要异常场景和边界条件
- 性能测试：覆盖高并发和性能关键点
- 安全测试：覆盖主要安全风险点
- 兼容性测试：覆盖多环境和版本兼容
- 生产验证：覆盖上线后关键验证点

**测试执行建议：**
1. 优先执行冒烟测试用例确保基本功能
2. 按需求模块并行执行功能测试用例
3. 在功能稳定后执行性能和安全测试
4. 上线前执行完整回归测试
5. 上线后执行生产验证用例
